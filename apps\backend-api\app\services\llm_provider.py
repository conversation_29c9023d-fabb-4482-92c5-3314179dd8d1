from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import openai
from openai import AzureOpenAI, OpenAI
import logging

from app.config import settings

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    @abstractmethod
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text"""
        pass
    
    @abstractmethod
    async def extract_context(self, text: str) -> str:
        """Extract structured context from invoice text"""
        pass
    
    @abstractmethod
    async def suggest_accounting(self, context: str, similar_contexts: List[str]) -> Dict[str, Any]:
        """Suggest accounting entries based on context and similar invoices"""
        pass


class OpenAIProvider(LLMProvider):
    """OpenAI provider implementation"""
    
    def __init__(self):
        if not settings.openai_api_key:
            raise ValueError("OpenAI API key not configured")
        
        self.client = OpenAI(api_key=settings.openai_api_key)
        self.model = settings.openai_model
        self.embedding_model = "text-embedding-3-small"
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding using OpenAI"""
        try:
            response = self.client.embeddings.create(
                model=self.embedding_model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise
    
    async def extract_context(self, text: str) -> str:
        """Extract structured context from invoice text"""
        prompt = """
        Extract key information from this invoice text and return it in a structured format.
        Focus on: supplier name, invoice number, date, amount, currency, line items, and any other relevant details.
        
        Invoice text:
        {text}
        
        Return the information in a clear, structured format that can be used for accounting purposes.
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at extracting structured information from invoices."},
                    {"role": "user", "content": prompt.format(text=text)}
                ],
                temperature=0.1
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error extracting context: {e}")
            raise
    
    async def suggest_accounting(self, context: str, similar_contexts: List[str]) -> Dict[str, Any]:
        """Suggest accounting entries based on context and similar invoices"""
        similar_examples = "\n\n".join([f"Example {i+1}:\n{ctx}" for i, ctx in enumerate(similar_contexts)])
        
        prompt = f"""
        Based on the invoice context and similar historical invoices, suggest appropriate accounting entries.
        
        Current Invoice Context:
        {context}
        
        Similar Historical Invoices:
        {similar_examples}
        
        Please provide:
        1. Suggested account codes and names
        2. Debit and credit amounts
        3. Description for each entry
        4. Confidence score (0.0 to 1.0) for each suggestion
        
        Return the response in JSON format with the following structure:
        {{
            "entries": [
                {{
                    "account_code": "string",
                    "account_name": "string", 
                    "debit_amount": number or null,
                    "credit_amount": number or null,
                    "description": "string",
                    "confidence_score": number
                }}
            ],
            "overall_confidence": number,
            "reasoning": "string"
        }}
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert accountant who suggests accounting entries for invoices. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            import json
            return json.loads(response.choices[0].message.content)
        except Exception as e:
            logger.error(f"Error suggesting accounting: {e}")
            raise


class AzureOpenAIProvider(LLMProvider):
    """Azure OpenAI provider implementation"""
    
    def __init__(self):
        if not settings.azure_openai_api_key or not settings.azure_openai_endpoint:
            raise ValueError("Azure OpenAI configuration not complete")
        
        self.client = AzureOpenAI(
            api_key=settings.azure_openai_api_key,
            api_version="2024-02-01",
            azure_endpoint=settings.azure_openai_endpoint
        )
        self.model = settings.azure_openai_deployment_name
        self.embedding_model = "text-embedding-3-small"  # Adjust based on your deployment
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding using Azure OpenAI"""
        try:
            response = self.client.embeddings.create(
                model=self.embedding_model,
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            raise
    
    async def extract_context(self, text: str) -> str:
        """Extract structured context from invoice text"""
        prompt = """
        Extract key information from this invoice text and return it in a structured format.
        Focus on: supplier name, invoice number, date, amount, currency, line items, and any other relevant details.
        
        Invoice text:
        {text}
        
        Return the information in a clear, structured format that can be used for accounting purposes.
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert at extracting structured information from invoices."},
                    {"role": "user", "content": prompt.format(text=text)}
                ],
                temperature=0.1
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"Error extracting context: {e}")
            raise
    
    async def suggest_accounting(self, context: str, similar_contexts: List[str]) -> Dict[str, Any]:
        """Suggest accounting entries based on context and similar invoices"""
        similar_examples = "\n\n".join([f"Example {i+1}:\n{ctx}" for i, ctx in enumerate(similar_contexts)])
        
        prompt = f"""
        Based on the invoice context and similar historical invoices, suggest appropriate accounting entries.
        
        Current Invoice Context:
        {context}
        
        Similar Historical Invoices:
        {similar_examples}
        
        Please provide:
        1. Suggested account codes and names
        2. Debit and credit amounts
        3. Description for each entry
        4. Confidence score (0.0 to 1.0) for each suggestion
        
        Return the response in JSON format with the following structure:
        {{
            "entries": [
                {{
                    "account_code": "string",
                    "account_name": "string", 
                    "debit_amount": number or null,
                    "credit_amount": number or null,
                    "description": "string",
                    "confidence_score": number
                }}
            ],
            "overall_confidence": number,
            "reasoning": "string"
        }}
        """
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an expert accountant who suggests accounting entries for invoices. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            import json
            return json.loads(response.choices[0].message.content)
        except Exception as e:
            logger.error(f"Error suggesting accounting: {e}")
            raise


def get_llm_provider() -> LLMProvider:
    """Factory function to get the configured LLM provider"""
    provider_name = settings.llm_provider.lower()
    
    if provider_name == "openai":
        return OpenAIProvider()
    elif provider_name == "azure_openai":
        return AzureOpenAIProvider()
    else:
        raise ValueError(f"Unknown LLM provider: {provider_name}")


# Global provider instance
_provider_instance = None


def get_provider_instance() -> LLMProvider:
    """Get singleton provider instance"""
    global _provider_instance
    if _provider_instance is None:
        _provider_instance = get_llm_provider()
    return _provider_instance
