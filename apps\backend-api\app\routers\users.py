from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID

from app.database import get_db
from app.models.user import User, TenantUser, Role
from app.middleware import get_current_tenant_user
from app.utils.permissions import Permission, check_permission

router = APIRouter()


@router.get("/", response_model=List[dict])
async def list_tenant_users(
    skip: int = 0,
    limit: int = 100,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """List users in current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.USERS_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    tenant_users = db.query(TenantUser).filter(
        TenantUser.tenant_id == tenant_user.tenant_id,
        TenantUser.is_active == True
    ).offset(skip).limit(limit).all()
    
    return [
        {
            "id": tu.user.id,
            "email": tu.user.email,
            "is_active": tu.user.is_active,
            "is_2fa_enabled": tu.user.is_2fa_enabled,
            "role": {
                "id": tu.role.id,
                "name": tu.role.name,
                "description": tu.role.description
            },
            "created_at": tu.created_at
        }
        for tu in tenant_users
    ]


@router.get("/{user_id}")
async def get_user(
    user_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Get user details"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.USERS_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    # Find user in current tenant
    target_tenant_user = db.query(TenantUser).filter(
        TenantUser.user_id == user_id,
        TenantUser.tenant_id == tenant_user.tenant_id,
        TenantUser.is_active == True
    ).first()
    
    if not target_tenant_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in this tenant"
        )
    
    user = target_tenant_user.user
    
    return {
        "id": user.id,
        "email": user.email,
        "is_active": user.is_active,
        "is_2fa_enabled": user.is_2fa_enabled,
        "role": {
            "id": target_tenant_user.role.id,
            "name": target_tenant_user.role.name,
            "description": target_tenant_user.role.description,
            "permissions": target_tenant_user.role.permissions
        },
        "created_at": target_tenant_user.created_at,
        "updated_at": target_tenant_user.updated_at
    }


@router.put("/{user_id}/role")
async def update_user_role(
    user_id: UUID,
    role_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Update user's role in current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.USERS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    # Find user in current tenant
    target_tenant_user = db.query(TenantUser).filter(
        TenantUser.user_id == user_id,
        TenantUser.tenant_id == tenant_user.tenant_id,
        TenantUser.is_active == True
    ).first()
    
    if not target_tenant_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in this tenant"
        )
    
    # Verify role exists
    role = db.query(Role).filter(Role.id == role_id).first()
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    # Update role
    target_tenant_user.role_id = role_id
    db.commit()
    
    return {"message": "User role updated successfully"}


@router.put("/{user_id}/deactivate")
async def deactivate_user(
    user_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Deactivate user in current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.USERS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    # Prevent self-deactivation
    if user_id == tenant_user.user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate yourself"
        )
    
    # Find user in current tenant
    target_tenant_user = db.query(TenantUser).filter(
        TenantUser.user_id == user_id,
        TenantUser.tenant_id == tenant_user.tenant_id,
        TenantUser.is_active == True
    ).first()
    
    if not target_tenant_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in this tenant"
        )
    
    # Deactivate
    target_tenant_user.is_active = False
    db.commit()
    
    return {"message": "User deactivated successfully"}


@router.put("/{user_id}/activate")
async def activate_user(
    user_id: UUID,
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """Activate user in current tenant"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.USERS_WRITE):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    # Find user in current tenant
    target_tenant_user = db.query(TenantUser).filter(
        TenantUser.user_id == user_id,
        TenantUser.tenant_id == tenant_user.tenant_id
    ).first()
    
    if not target_tenant_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found in this tenant"
        )
    
    # Activate
    target_tenant_user.is_active = True
    db.commit()
    
    return {"message": "User activated successfully"}


@router.get("/roles/", response_model=List[dict])
async def list_roles(
    tenant_user: TenantUser = Depends(get_current_tenant_user),
    db: Session = Depends(get_db)
):
    """List available roles"""
    # Check permissions
    if not check_permission(tenant_user.role.permissions, Permission.USERS_READ):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Permission denied"
        )
    
    roles = db.query(Role).all()
    
    return [
        {
            "id": role.id,
            "name": role.name,
            "description": role.description,
            "permissions": role.permissions
        }
        for role in roles
    ]
