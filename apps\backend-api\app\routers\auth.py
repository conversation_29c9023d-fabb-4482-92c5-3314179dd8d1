from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import List, Annotated

from app.database import get_db
from app.models.user import User, TenantUser
from app.schemas.auth import (
    LoginRequest, LoginResponse, TwoFAVerifyRequest, TwoFASetupResponse,
    TwoFAEnableRequest, TwoFADisableRequest, TokenResponse, UserInfo
)
from app.utils.security import (
    verify_password, create_access_token, create_temp_token, create_refresh_token,
    generate_totp_secret, generate_totp_qr_code, verify_totp_code,
    generate_recovery_codes, hash_recovery_code, verify_recovery_code
)
from app.middleware import get_current_active_user, get_user_from_temp_token, security

router = APIRouter()


@router.post("/token", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: Session = Depends(get_db)):
    """Login endpoint with 2FA support"""
    # Find user by email
    user = db.query(User).filter(User.email == login_data.email).first()
    
    if not user or not verify_password(login_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled"
        )
    
    # Check if 2FA is enabled
    if user.is_2fa_enabled:
        # Return temporary token for 2FA verification
        temp_token = create_temp_token({"sub": str(user.id)})
        return LoginResponse(
            temp_token=temp_token,
            requires_2fa=True,
            message="2FA verification required"
        )
    else:
        # Return full access token
        access_token = create_access_token({"sub": str(user.id)})
        return LoginResponse(
            access_token=access_token,
            requires_2fa=False,
            message="Login successful"
        )


@router.post("/2fa/verify", response_model=TokenResponse)
async def verify_2fa(
    verify_data: TwoFAVerifyRequest,
    user: User = Depends(get_user_from_temp_token),
    db: Session = Depends(get_db)
):
    """Verify 2FA code and return full tokens"""
    if not user.is_2fa_enabled or not user.mfa_secret:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="2FA is not enabled for this user"
        )
    
    # Try TOTP code first
    if verify_totp_code(user.mfa_secret, verify_data.code):
        # Generate tokens
        access_token = create_access_token({"sub": str(user.id)})
        refresh_token = create_refresh_token({"sub": str(user.id)})
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token
        )
    
    # Try recovery code
    if user.mfa_recovery_codes:
        recovery_codes = user.mfa_recovery_codes
        if verify_recovery_code(verify_data.code, recovery_codes):
            # Remove used recovery code
            code_hash = hash_recovery_code(verify_data.code)
            recovery_codes.remove(code_hash)
            user.mfa_recovery_codes = recovery_codes
            db.commit()
            
            # Generate tokens
            access_token = create_access_token({"sub": str(user.id)})
            refresh_token = create_refresh_token({"sub": str(user.id)})
            
            return TokenResponse(
                access_token=access_token,
                refresh_token=refresh_token
            )
    
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Invalid 2FA code"
    )


@router.post("/2fa/setup", response_model=TwoFASetupResponse)
async def setup_2fa(
    user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Setup 2FA for user"""
    if user.is_2fa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="2FA is already enabled"
        )
    
    # Generate secret and QR code
    secret = generate_totp_secret()
    qr_code = generate_totp_qr_code(secret, user.email)
    
    # Generate recovery codes
    recovery_codes = generate_recovery_codes()
    hashed_codes = [hash_recovery_code(code) for code in recovery_codes]
    
    # Store secret temporarily (not enabled yet)
    user.mfa_secret = secret
    user.mfa_recovery_codes = hashed_codes
    db.commit()
    
    return TwoFASetupResponse(
        secret=secret,
        qr_code=qr_code,
        backup_codes=recovery_codes
    )


@router.post("/2fa/enable")
async def enable_2fa(
    enable_data: TwoFAEnableRequest,
    user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Enable 2FA after verification"""
    if user.is_2fa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="2FA is already enabled"
        )
    
    if not user.mfa_secret:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="2FA setup not initiated"
        )
    
    # Verify the code
    if not verify_totp_code(user.mfa_secret, enable_data.code):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid 2FA code"
        )
    
    # Enable 2FA
    user.is_2fa_enabled = True
    db.commit()
    
    return {"message": "2FA enabled successfully"}


@router.post("/2fa/disable")
async def disable_2fa(
    disable_data: TwoFADisableRequest,
    user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Disable 2FA"""
    if not user.is_2fa_enabled:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="2FA is not enabled"
        )
    
    # Verify password
    if not verify_password(disable_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid password"
        )
    
    # Verify 2FA code
    if not verify_totp_code(user.mfa_secret, disable_data.code):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid 2FA code"
        )
    
    # Disable 2FA
    user.is_2fa_enabled = False
    user.mfa_secret = None
    user.mfa_recovery_codes = None
    db.commit()
    
    return {"message": "2FA disabled successfully"}


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(
    user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get current user information"""
    # Get user's tenants
    tenant_users = db.query(TenantUser).filter(
        TenantUser.user_id == user.id,
        TenantUser.is_active == True
    ).all()
    
    tenants = []
    for tu in tenant_users:
        tenants.append({
            "id": tu.tenant_id,
            "name": tu.tenant.name,
            "role": tu.role.name,
            "permissions": tu.role.permissions
        })
    
    return UserInfo(
        id=user.id,
        email=user.email,
        is_2fa_enabled=user.is_2fa_enabled,
        tenants=tenants
    )
