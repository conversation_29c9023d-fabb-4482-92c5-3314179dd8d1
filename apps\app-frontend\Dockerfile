FROM node:18-alpine

WORKDIR /app

# Copy root package files for workspace setup
COPY package*.json ./
COPY nx.json ./
COPY tsconfig.base.json ./

# Copy shared packages
COPY packages/ ./packages/

# Copy this app's package files and source
COPY apps/app-frontend/ ./apps/app-frontend/

# Install dependencies from root (this will install shared packages too)
RUN npm install --legacy-peer-deps

# Create symlinks for shared packages in node_modules
RUN mkdir -p /app/apps/app-frontend/node_modules/@aggie
RUN ln -sf /app/packages/shared-types /app/apps/app-frontend/node_modules/@aggie/shared-types
RUN ln -sf /app/packages/ui-components /app/apps/app-frontend/node_modules/@aggie/ui-components

# Set working directory to the app
WORKDIR /app/apps/app-frontend

EXPOSE 3002

# Set the port environment variable
ENV PORT=3002

CMD ["npm", "start"]
