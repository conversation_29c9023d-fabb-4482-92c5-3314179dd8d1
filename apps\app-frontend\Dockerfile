# Multi-stage build for app-frontend
# Development stage
FROM node:18-alpine AS development

# Install development tools
RUN apk add --no-cache git

WORKDIR /app

# Copy root package files for workspace setup
COPY package*.json ./
COPY nx.json ./
COPY tsconfig.base.json ./

# Copy shared packages first for better caching
COPY packages/ ./packages/

# Install dependencies from root (this will install shared packages too)
RUN npm install --legacy-peer-deps

# Copy this app's package files and source
COPY apps/app-frontend/ ./apps/app-frontend/

# Create symlinks for shared packages in node_modules
RUN mkdir -p /app/apps/app-frontend/node_modules/@aggie
RUN ln -sf /app/packages/shared-types /app/apps/app-frontend/node_modules/@aggie/shared-types
RUN ln -sf /app/packages/ui-components /app/apps/app-frontend/node_modules/@aggie/ui-components

# Set working directory to the app
WORKDIR /app/apps/app-frontend

# Expose port
EXPOSE 3002

# Set environment variables for development
ENV PORT=3002
ENV NODE_ENV=development
ENV CHOKIDAR_USEPOLLING=true
ENV FAST_REFRESH=true

# Development command with hot reload
CMD ["npm", "start"]

# Production build stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy root package files
COPY package*.json ./
COPY nx.json ./
COPY tsconfig.base.json ./

# Copy shared packages
COPY packages/ ./packages/

# Install dependencies
RUN npm ci --legacy-peer-deps --only=production

# Copy app source
COPY apps/app-frontend/ ./apps/app-frontend/

# Create symlinks for shared packages
RUN mkdir -p /app/apps/app-frontend/node_modules/@aggie
RUN ln -sf /app/packages/shared-types /app/apps/app-frontend/node_modules/@aggie/shared-types
RUN ln -sf /app/packages/ui-components /app/apps/app-frontend/node_modules/@aggie/ui-components

# Set working directory and build
WORKDIR /app/apps/app-frontend
RUN npm run build

# Production stage
FROM nginx:alpine AS production

# Copy built app from build stage
COPY --from=build /app/apps/app-frontend/build /usr/share/nginx/html

# Copy nginx configuration if needed
# COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]

# Default to development stage
FROM development
