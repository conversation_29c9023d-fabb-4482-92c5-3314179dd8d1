import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import PyPDF2
import io
import logging
from typing import Optional

logger = logging.getLogger(__name__)


class OCRService:
    """Service for extracting text from various file formats"""
    
    def __init__(self):
        # Configure Tesseract (adjust path if needed)
        # pytesseract.pytesseract.tesseract_cmd = r'/usr/bin/tesseract'
        pass
    
    def extract_text_from_file(self, file_path: str, file_type: str) -> str:
        """Extract text from file based on type"""
        try:
            if file_type.lower() == 'pdf':
                return self._extract_from_pdf(file_path)
            elif file_type.lower() in ['png', 'jpg', 'jpeg']:
                return self._extract_from_image(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            raise
    
    def _extract_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        text = ""
        
        # First try to extract text directly (for PDFs with selectable text)
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    page_text = page.extract_text()
                    if page_text.strip():
                        text += page_text + "\n"
                
                # If we got meaningful text, return it
                if len(text.strip()) > 50:  # Arbitrary threshold
                    logger.info(f"Extracted text directly from PDF: {len(text)} characters")
                    return text.strip()
        except Exception as e:
            logger.warning(f"Direct PDF text extraction failed: {e}")
        
        # If direct extraction failed or yielded little text, use OCR
        logger.info("Falling back to OCR for PDF")
        return self._ocr_pdf(file_path)
    
    def _ocr_pdf(self, file_path: str) -> str:
        """Extract text from PDF using OCR"""
        try:
            # Convert PDF pages to images
            images = convert_from_path(file_path, dpi=300)
            
            text = ""
            for i, image in enumerate(images):
                logger.debug(f"Processing page {i+1} of PDF")
                page_text = pytesseract.image_to_string(
                    image, 
                    lang='eng+swe',  # English and Swedish
                    config='--psm 6'  # Assume uniform block of text
                )
                text += page_text + "\n"
            
            logger.info(f"OCR extracted {len(text)} characters from PDF")
            return text.strip()
        except Exception as e:
            logger.error(f"OCR PDF extraction failed: {e}")
            raise
    
    def _extract_from_image(self, file_path: str) -> str:
        """Extract text from image file using OCR"""
        try:
            # Open and preprocess image
            image = Image.open(file_path)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Enhance image for better OCR results
            image = self._preprocess_image(image)
            
            # Extract text using Tesseract
            text = pytesseract.image_to_string(
                image,
                lang='eng+swe',  # English and Swedish
                config='--psm 6'  # Assume uniform block of text
            )
            
            logger.info(f"OCR extracted {len(text)} characters from image")
            return text.strip()
        except Exception as e:
            logger.error(f"Image OCR extraction failed: {e}")
            raise
    
    def _preprocess_image(self, image: Image.Image) -> Image.Image:
        """Preprocess image to improve OCR accuracy"""
        try:
            # Resize if image is too small
            width, height = image.size
            if width < 1000 or height < 1000:
                scale_factor = max(1000 / width, 1000 / height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to grayscale for better OCR
            image = image.convert('L')
            
            # Enhance contrast
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)
            
            return image
        except Exception as e:
            logger.warning(f"Image preprocessing failed: {e}")
            return image  # Return original if preprocessing fails
    
    def validate_extracted_text(self, text: str) -> bool:
        """Validate that extracted text looks like an invoice"""
        if not text or len(text.strip()) < 20:
            return False
        
        # Look for common invoice keywords
        invoice_keywords = [
            'invoice', 'faktura', 'bill', 'receipt', 'kvitto',
            'amount', 'belopp', 'total', 'summa', 'date', 'datum',
            'supplier', 'leverantör', 'customer', 'kund'
        ]
        
        text_lower = text.lower()
        keyword_count = sum(1 for keyword in invoice_keywords if keyword in text_lower)
        
        # Should have at least 2 invoice-related keywords
        return keyword_count >= 2


# Global service instance
_ocr_service = None


def get_ocr_service() -> OCRService:
    """Get singleton OCR service instance"""
    global _ocr_service
    if _ocr_service is None:
        _ocr_service = OCRService()
    return _ocr_service
