{"apps/admin-frontend": {"root": ["apps/admin-frontend/project.json", "nx/core/project-json"], "name": ["apps/admin-frontend/project.json", "nx/core/project-json"], "tags": ["apps/admin-frontend/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.js": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/admin-frontend/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.executor": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.options": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.metadata": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.options.script": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.executor": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.options": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.options.script": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata.scriptContent": ["apps/admin-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata.runCommand": ["apps/admin-frontend/package.json", "nx/core/package-json"], "$schema": ["apps/admin-frontend/project.json", "nx/core/project-json"], "sourceRoot": ["apps/admin-frontend/project.json", "nx/core/project-json"], "projectType": ["apps/admin-frontend/project.json", "nx/core/project-json"], "tags.scope:admin": ["apps/admin-frontend/project.json", "nx/core/project-json"], "tags.type:app": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.outputs": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.compiler": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.index": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.baseHref": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.main": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.assets": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.styles": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.scripts": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.options.webpackConfig": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.vendorChunk": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.fileReplacements": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.optimization": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.sourceMap": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.namedChunks": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.extractLicenses": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.vendorChunk": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.executor": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.options": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.options.buildTarget": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.options.hmr": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.options.port": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.production.hmr": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.lint": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.lint.executor": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.lint.outputs": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.lint.options": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.lint.options.lintFilePatterns": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.executor": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.outputs": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.options": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.configurations": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.options.passWithNoTests": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.configurations.ci": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.configurations.ci.ci": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.test.configurations.ci.coverageReporters": ["apps/admin-frontend/project.json", "nx/core/project-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/app-frontend": {"root": ["apps/app-frontend/project.json", "nx/core/project-json"], "name": ["apps/app-frontend/project.json", "nx/core/project-json"], "tags": ["apps/app-frontend/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.js": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/app-frontend/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.executor": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.options": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.metadata": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.options.script": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.executor": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.options": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.options.script": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata.scriptContent": ["apps/app-frontend/package.json", "nx/core/package-json"], "targets.eject.metadata.runCommand": ["apps/app-frontend/package.json", "nx/core/package-json"], "$schema": ["apps/app-frontend/project.json", "nx/core/project-json"], "sourceRoot": ["apps/app-frontend/project.json", "nx/core/project-json"], "projectType": ["apps/app-frontend/project.json", "nx/core/project-json"], "tags.scope:app": ["apps/app-frontend/project.json", "nx/core/project-json"], "tags.type:app": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.outputs": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.defaultConfiguration": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.compiler": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.index": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.baseHref": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.main": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.polyfills": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.assets": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.styles": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.scripts": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.options.webpackConfig": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.extractLicenses": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.optimization": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.sourceMap": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.development.vendorChunk": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.fileReplacements": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.optimization": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.outputHashing": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.sourceMap": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.namedChunks": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.extractLicenses": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.configurations.production.vendorChunk": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.executor": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.defaultConfiguration": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.options": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.options.buildTarget": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.options.hmr": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.options.port": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.development": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.development.buildTarget": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.production": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.production.buildTarget": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.serve.configurations.production.hmr": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.lint": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.lint.executor": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.lint.outputs": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.lint.options": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.lint.options.lintFilePatterns": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.executor": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.outputs": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.options": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.configurations": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.options.passWithNoTests": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.configurations.ci": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.configurations.ci.ci": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.test.configurations.ci.coverageReporters": ["apps/app-frontend/project.json", "nx/core/project-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/shared-types": {"root": ["packages/shared-types/project.json", "nx/core/project-json"], "name": ["packages/shared-types/project.json", "nx/core/project-json"], "tags": ["packages/shared-types/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.js": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/shared-types/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/shared-types/package.json", "nx/core/package-json"], "targets": ["packages/shared-types/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["packages/shared-types/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["packages/shared-types/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["packages/shared-types/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["packages/shared-types/package.json", "nx/core/package-json"], "$schema": ["packages/shared-types/project.json", "nx/core/project-json"], "sourceRoot": ["packages/shared-types/project.json", "nx/core/project-json"], "projectType": ["packages/shared-types/project.json", "nx/core/project-json"], "tags.scope:shared": ["packages/shared-types/project.json", "nx/core/project-json"], "tags.type:lib": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build.executor": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build.outputs": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build.options": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build.options.main": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build.options.assets": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.lint": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.lint.executor": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.lint.outputs": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.lint.options": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.lint.options.lintFilePatterns": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.test": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.test.executor": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.test.outputs": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.test.options": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.test.options.passWithNoTests": ["packages/shared-types/project.json", "nx/core/project-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/ui-components": {"root": ["packages/ui-components/project.json", "nx/core/project-json"], "name": ["packages/ui-components/project.json", "nx/core/project-json"], "tags": ["packages/ui-components/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.js": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ui-components/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ui-components/package.json", "nx/core/package-json"], "targets": ["packages/ui-components/package.json", "nx/core/package-json"], "targets.nx-release-publish": ["packages/ui-components/package.json", "nx/core/package-json"], "targets.nx-release-publish.executor": ["packages/ui-components/package.json", "nx/core/package-json"], "targets.nx-release-publish.dependsOn": ["packages/ui-components/package.json", "nx/core/package-json"], "targets.nx-release-publish.options": ["packages/ui-components/package.json", "nx/core/package-json"], "$schema": ["packages/ui-components/project.json", "nx/core/project-json"], "sourceRoot": ["packages/ui-components/project.json", "nx/core/project-json"], "projectType": ["packages/ui-components/project.json", "nx/core/project-json"], "tags.scope:shared": ["packages/ui-components/project.json", "nx/core/project-json"], "tags.type:lib": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build.executor": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build.outputs": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build.options": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build.options.outputPath": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build.options.main": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build.options.tsConfig": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build.options.assets": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.lint": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.lint.executor": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.lint.outputs": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.lint.options": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.lint.options.lintFilePatterns": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.test": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.test.executor": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.test.outputs": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.test.options": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.test.options.jestConfig": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.test.options.passWithNoTests": ["packages/ui-components/project.json", "nx/core/project-json"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.inputs": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/backend-api": {"root": ["apps/backend-api/project.json", "nx/core/project-json"], "name": ["apps/backend-api/project.json", "nx/core/project-json"], "$schema": ["apps/backend-api/project.json", "nx/core/project-json"], "sourceRoot": ["apps/backend-api/project.json", "nx/core/project-json"], "projectType": ["apps/backend-api/project.json", "nx/core/project-json"], "tags": ["apps/backend-api/project.json", "nx/core/project-json"], "tags.scope:backend": ["apps/backend-api/project.json", "nx/core/project-json"], "tags.type:app": ["apps/backend-api/project.json", "nx/core/project-json"], "targets": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.serve": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.serve.executor": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.serve.options": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.serve.options.command": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.serve.options.cwd": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.test": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.test.executor": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.test.options": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.test.options.command": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.test.options.cwd": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.lint": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.lint.executor": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.lint.options": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.lint.options.command": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.lint.options.cwd": ["apps/backend-api/project.json", "nx/core/project-json"], "targets.test.inputs": ["nx.json", "nx/target-defaults"], "targets.test.parallelism": ["nx.json", "nx/target-defaults"], "targets.lint.inputs": ["nx.json", "nx/target-defaults"], "targets.lint.parallelism": ["nx.json", "nx/target-defaults"]}}