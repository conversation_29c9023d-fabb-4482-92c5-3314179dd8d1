from sqlalchemy import Column, String, Text, Float, <PERSON>olean, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from pgvector.sqlalchemy import Vector
from .base import TenantBaseModel


class Invoice(TenantBaseModel):
    """Invoice model with tenant isolation"""
    __tablename__ = "invoices"

    supplier_name = Column(String(255), nullable=False)
    invoice_number = Column(String(100), nullable=True)
    invoice_date = Column(String(50), nullable=True)  # Extracted as string initially
    due_date = Column(String(50), nullable=True)
    total_amount = Column(Float, nullable=True)
    currency = Column(String(10), nullable=True)
    
    # File information
    original_filename = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_type = Column(String(50), nullable=False)  # pdf, png, jpg
    
    # Processing data
    raw_data = Column(JSON, nullable=True)  # Original extracted data
    extracted_text = Column(Text, nullable=True)  # OCR/PDF text
    extracted_context = Column(Text, nullable=True)  # LLM-processed context
    
    # Status tracking
    status = Column(String(50), nullable=False, default="pending")  # pending, processing, completed, failed, needs_review
    processing_error = Column(Text, nullable=True)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="invoices")
    accounting_entries = relationship("AccountingEntry", back_populates="invoice", cascade="all, delete-orphan")
    invoice_vectors = relationship("InvoiceVector", back_populates="invoice", cascade="all, delete-orphan")
    action_items = relationship("ActionItem", back_populates="invoice")

    def __repr__(self):
        return f"<Invoice(id={self.id}, supplier='{self.supplier_name}', status='{self.status}')>"


class AccountingEntry(TenantBaseModel):
    """Accounting entry suggestions from AI"""
    __tablename__ = "accounting_entries"

    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=False)
    
    # Accounting data
    account_code = Column(String(50), nullable=False)
    account_name = Column(String(255), nullable=False)
    debit_amount = Column(Float, nullable=True)
    credit_amount = Column(Float, nullable=True)
    description = Column(Text, nullable=True)
    
    # AI confidence and validation
    confidence_score = Column(Float, nullable=False, default=0.0)  # 0.0 to 1.0
    is_validated = Column(Boolean, default=False, nullable=False)
    validated_by = Column(UUID(as_uuid=True), nullable=True)  # User ID who validated
    
    # Additional metadata
    entry_data = Column(JSON, nullable=True)  # Additional structured data
    
    # Relationships
    invoice = relationship("Invoice", back_populates="accounting_entries")

    def __repr__(self):
        return f"<AccountingEntry(id={self.id}, account='{self.account_code}', confidence={self.confidence_score})>"


class InvoiceVector(TenantBaseModel):
    """Vector embeddings for RAG"""
    __tablename__ = "invoice_vectors"

    invoice_id = Column(UUID(as_uuid=True), ForeignKey("invoices.id"), nullable=False)
    embedding = Column(Vector(1536), nullable=False)  # OpenAI embedding dimension
    content_hash = Column(String(64), nullable=False)  # Hash of the content used for embedding
    
    # Relationships
    invoice = relationship("Invoice", back_populates="invoice_vectors")

    def __repr__(self):
        return f"<InvoiceVector(id={self.id}, invoice_id={self.invoice_id})>"
