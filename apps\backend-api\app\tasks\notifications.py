from celery import current_task
from sqlalchemy.orm import Session
import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from typing import Optional

from app.celery_app import celery_app
from app.database import SessionLocal, set_tenant_context
from app.models.action_item import ActionItem
from app.models.user import User
from app.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending email notifications"""
    
    def __init__(self):
        self.smtp_host = settings.smtp_host
        self.smtp_port = settings.smtp_port
        self.smtp_username = settings.smtp_username
        self.smtp_password = settings.smtp_password
        self.from_email = settings.smtp_from_email
    
    def send_email(self, to_email: str, subject: str, body: str, html_body: Optional[str] = None) -> bool:
        """Send email notification"""
        try:
            if not all([self.smtp_host, self.smtp_username, self.smtp_password, self.from_email]):
                logger.warning("Email configuration incomplete, skipping email send")
                return False
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.from_email
            msg['To'] = to_email
            
            # Add text part
            text_part = MIMEText(body, 'plain')
            msg.attach(text_part)
            
            # Add HTML part if provided
            if html_body:
                html_part = MIMEText(html_body, 'html')
                msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                server.starttls()
                server.login(self.smtp_username, self.smtp_password)
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {e}")
            return False


@celery_app.task(bind=True)
def send_action_item_notification_task(self, action_item_id: str):
    """Send notification for new action item"""
    db = SessionLocal()
    try:
        # Get action item
        action_item = db.query(ActionItem).filter(ActionItem.id == action_item_id).first()
        if not action_item:
            logger.error(f"Action item {action_item_id} not found")
            return {"error": "Action item not found"}
        
        # Set tenant context
        set_tenant_context(db, str(action_item.tenant_id))
        
        # Get assigned user
        user = db.query(User).filter(User.id == action_item.user_id).first()
        if not user:
            logger.error(f"User {action_item.user_id} not found")
            return {"error": "User not found"}
        
        # Prepare email content
        subject = f"New Action Item: {action_item.title}"
        
        # Text version
        body = f"""
Hello,

You have been assigned a new action item in Aggie:

Title: {action_item.title}
Priority: {action_item.priority.upper()}
Category: {action_item.category}

Description:
{action_item.description}

Please log in to Aggie to review and complete this action item.

Best regards,
Aggie Team
        """.strip()
        
        # HTML version
        html_body = f"""
        <html>
        <body>
            <h2>New Action Item Assigned</h2>
            
            <p>Hello,</p>
            
            <p>You have been assigned a new action item in Aggie:</p>
            
            <table style="border-collapse: collapse; width: 100%; max-width: 500px;">
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Title:</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{action_item.title}</td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Priority:</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">
                        <span style="color: {'red' if action_item.priority == 'urgent' else 'orange' if action_item.priority == 'high' else 'blue'};">
                            {action_item.priority.upper()}
                        </span>
                    </td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 8px; font-weight: bold;">Category:</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{action_item.category}</td>
                </tr>
            </table>
            
            <h3>Description:</h3>
            <p>{action_item.description}</p>
            
            <p>Please log in to Aggie to review and complete this action item.</p>
            
            <p>Best regards,<br>Aggie Team</p>
        </body>
        </html>
        """
        
        # Send email
        email_service = EmailService()
        success = email_service.send_email(user.email, subject, body, html_body)
        
        if success:
            logger.info(f"Action item notification sent to {user.email}")
            return {"status": "success", "recipient": user.email}
        else:
            logger.error(f"Failed to send action item notification to {user.email}")
            return {"status": "failed", "recipient": user.email}
        
    except Exception as e:
        logger.error(f"Error sending action item notification: {e}")
        raise
    finally:
        db.close()


@celery_app.task(bind=True)
def send_invoice_processing_notification_task(self, invoice_id: str, status: str):
    """Send notification when invoice processing is completed"""
    db = SessionLocal()
    try:
        from app.models.invoice import Invoice
        from app.models.user import TenantUser, Role
        
        # Get invoice
        invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
        if not invoice:
            logger.error(f"Invoice {invoice_id} not found")
            return {"error": "Invoice not found"}
        
        # Set tenant context
        set_tenant_context(db, str(invoice.tenant_id))
        
        # Get users who should be notified (admins and managers)
        tenant_users = db.query(TenantUser).join(Role).filter(
            TenantUser.tenant_id == invoice.tenant_id,
            TenantUser.is_active == True,
            Role.name.in_(['admin', 'manager'])
        ).all()
        
        if not tenant_users:
            logger.warning(f"No users to notify for invoice {invoice_id}")
            return {"status": "no_recipients"}
        
        # Prepare email content based on status
        if status == "completed":
            subject = f"Invoice Processing Completed: {invoice.supplier_name}"
            message = f"Invoice {invoice.invoice_number or 'N/A'} from {invoice.supplier_name} has been successfully processed and is ready for review."
        elif status == "failed":
            subject = f"Invoice Processing Failed: {invoice.supplier_name}"
            message = f"Invoice {invoice.invoice_number or 'N/A'} from {invoice.supplier_name} failed to process automatically. Manual intervention required."
        else:
            subject = f"Invoice Status Update: {invoice.supplier_name}"
            message = f"Invoice {invoice.invoice_number or 'N/A'} from {invoice.supplier_name} status updated to: {status}"
        
        body = f"""
Hello,

{message}

Invoice Details:
- Supplier: {invoice.supplier_name}
- Invoice Number: {invoice.invoice_number or 'N/A'}
- Amount: {invoice.total_amount} {invoice.currency or ''} 
- Status: {status}
- File: {invoice.original_filename}

Please log in to Aggie to review the details.

Best regards,
Aggie Team
        """.strip()
        
        # Send to all relevant users
        email_service = EmailService()
        sent_count = 0
        
        for tenant_user in tenant_users:
            user = tenant_user.user
            if email_service.send_email(user.email, subject, body):
                sent_count += 1
        
        logger.info(f"Invoice processing notification sent to {sent_count} users")
        return {"status": "success", "sent_count": sent_count}
        
    except Exception as e:
        logger.error(f"Error sending invoice processing notification: {e}")
        raise
    finally:
        db.close()


@celery_app.task(bind=True)
def send_daily_summary_task(self, tenant_id: str):
    """Send daily summary of activities to tenant admins"""
    db = SessionLocal()
    try:
        from app.models.invoice import Invoice
        from app.models.user import TenantUser, Role
        from datetime import datetime, timedelta
        
        # Set tenant context
        set_tenant_context(db, tenant_id)
        
        # Get yesterday's date range
        yesterday = datetime.now() - timedelta(days=1)
        start_of_day = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_of_day = yesterday.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        # Get statistics
        total_invoices = db.query(Invoice).filter(
            Invoice.tenant_id == tenant_id,
            Invoice.created_at >= start_of_day,
            Invoice.created_at <= end_of_day
        ).count()
        
        completed_invoices = db.query(Invoice).filter(
            Invoice.tenant_id == tenant_id,
            Invoice.created_at >= start_of_day,
            Invoice.created_at <= end_of_day,
            Invoice.status == "completed"
        ).count()
        
        failed_invoices = db.query(Invoice).filter(
            Invoice.tenant_id == tenant_id,
            Invoice.created_at >= start_of_day,
            Invoice.created_at <= end_of_day,
            Invoice.status == "failed"
        ).count()
        
        pending_action_items = db.query(ActionItem).filter(
            ActionItem.tenant_id == tenant_id,
            ActionItem.is_completed == False
        ).count()
        
        # Get admin users
        admin_users = db.query(TenantUser).join(Role).filter(
            TenantUser.tenant_id == tenant_id,
            TenantUser.is_active == True,
            Role.name == 'admin'
        ).all()
        
        if not admin_users:
            logger.info(f"No admin users found for tenant {tenant_id}")
            return {"status": "no_recipients"}
        
        # Prepare email
        subject = f"Daily Summary - {yesterday.strftime('%Y-%m-%d')}"
        
        body = f"""
Daily Summary for {yesterday.strftime('%Y-%m-%d')}

Invoice Processing:
- Total invoices processed: {total_invoices}
- Successfully completed: {completed_invoices}
- Failed processing: {failed_invoices}
- Success rate: {(completed_invoices/total_invoices*100) if total_invoices > 0 else 0:.1f}%

Action Items:
- Pending action items: {pending_action_items}

Please log in to Aggie for detailed reports and to address any pending items.

Best regards,
Aggie Team
        """.strip()
        
        # Send to admin users
        email_service = EmailService()
        sent_count = 0
        
        for tenant_user in admin_users:
            user = tenant_user.user
            if email_service.send_email(user.email, subject, body):
                sent_count += 1
        
        logger.info(f"Daily summary sent to {sent_count} admin users for tenant {tenant_id}")
        return {"status": "success", "sent_count": sent_count}
        
    except Exception as e:
        logger.error(f"Error sending daily summary for tenant {tenant_id}: {e}")
        raise
    finally:
        db.close()
